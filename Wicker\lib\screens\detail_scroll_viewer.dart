import 'package:flutter/material.dart';
import '../widgets/category_feed_view.dart';

class DetailScrollViewer extends StatefulWidget {
  // Data for all categories
  final Map<String, List<Map<String, String>>> allCategoriesData;
  // Index of the category to show first
  final int initialCategoryIndex;
  // Index of the item within that category to show first
  final int initialItemIndex;

  const DetailScrollViewer({
    super.key,
    required this.allCategoriesData,
    required this.initialCategoryIndex,
    required this.initialItemIndex,
  });

  @override
  _DetailScrollViewerState createState() => _DetailScrollViewerState();
}

class _DetailScrollViewerState extends State<DetailScrollViewer> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialCategoryIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoryKeys = widget.allCategoriesData.keys.toList();

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: categoryKeys.length,
        itemBuilder: (context, index) {
          final categoryTitle = categoryKeys[index];
          final items = widget.allCategoriesData[categoryTitle]!;

          // For the first page, we use the specific item index the user clicked.
          // For all other pages, we just start from the beginning (index 0).
          final initialItemIdx = (index == widget.initialCategoryIndex)
              ? widget.initialItemIndex
              : 0;

          return CategoryFeedView(items: items, initialIndex: initialItemIdx);
        },
      ),
    );
  }
}
