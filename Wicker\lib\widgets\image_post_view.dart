import 'package:flutter/material.dart';

class ImagePostView extends StatelessWidget {
  final String imageUrl;
  const ImagePostView({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Image.network(
          imageUrl,
          fit: BoxFit.contain,
          // Show a loading indicator while the image loads
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Center(child: CircularProgressIndicator());
          },
        ),
      ),
    );
  }
}
