import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';

class VoiceNoteView extends StatefulWidget {
  final String audioUrl;
  final String title;
  const VoiceNoteView({super.key, required this.audioUrl, required this.title});

  @override
  _VoiceNoteViewState createState() => _VoiceNoteViewState();
}

class _VoiceNoteViewState extends State<VoiceNoteView> {
  late AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    // Pre-load the audio
    _audioPlayer.setUrl(widget.audioUrl);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      color: Colors.teal.shade700,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            widget.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          // A stream builder to react to player state changes (playing, paused, etc.)
          StreamBuilder<PlayerState>(
            stream: _audioPlayer.playerStateStream,
            builder: (context, snapshot) {
              final playerState = snapshot.data;
              final processingState = playerState?.processingState;
              final playing = playerState?.playing;

              if (processingState == ProcessingState.loading ||
                  processingState == ProcessingState.buffering) {
                return const CircularProgressIndicator(color: Colors.white);
              } else if (playing != true) {
                return IconButton(
                  icon: const Icon(Icons.play_arrow_rounded),
                  iconSize: 80,
                  color: Colors.white,
                  onPressed: _audioPlayer.play,
                );
              } else if (processingState != ProcessingState.completed) {
                return IconButton(
                  icon: const Icon(Icons.pause_rounded),
                  iconSize: 80,
                  color: Colors.white,
                  onPressed: _audioPlayer.pause,
                );
              } else {
                return IconButton(
                  icon: const Icon(Icons.replay_rounded),
                  iconSize: 80,
                  color: Colors.white,
                  onPressed: () => _audioPlayer.seek(Duration.zero),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
}
